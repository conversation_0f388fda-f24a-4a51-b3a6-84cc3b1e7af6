# Task ID: 26
# Title: Implement Local Alternatives for get_logs and get_advisors Tools
# Status: pending
# Dependencies: 4, 5
# Priority: medium
# Description: Create local equivalents for cloud-only features by implementing Docker-based log retrieval from local Supabase containers for get_logs and basic security and performance analysis using local database queries for get_advisors.
# Details:
This task involves creating local alternatives for two cloud-dependent tools:

1. **Local get_logs Implementation**:
   - Develop a Docker-based log retrieval system that connects to local Supabase containers
   - Use Docker API or CLI commands to extract logs from running Supabase containers
   - Implement log filtering and formatting to match the cloud version's output format
   - Create a consistent interface that works with both local and cloud environments
   - Example implementation:
   ```typescript
   async function getLocalLogs(options: LogOptions): Promise<LogEntry[]> {
     const { containerName, lines, filter } = options;
     const command = `docker logs --tail ${lines} ${containerName}`;
     const output = await execCommand(command);
     return parseAndFormatLogs(output, filter);
   }
   ```

2. **Local get_advisors Implementation**:
   - Implement database schema inspection using pg_catalog queries
   - Create security analysis functions that check for common issues:
     - Missing row-level security policies
     - Overly permissive roles and grants
     - Unencrypted sensitive columns
   - Implement performance analysis functions:
     - Missing indexes on frequently queried columns
     - Tables without primary keys
     - Inefficient query patterns
   - Example implementation:
   ```typescript
   async function getLocalAdvisors(options: AdvisorOptions): Promise<AdvisorResult[]> {
     const { category, severity } = options;
     const pool = await getDbConnectionPool();
     
     let advisors = [];
     if (category === 'security' || !category) {
       advisors = [...advisors, ...(await runSecurityChecks(pool))];
     }
     if (category === 'performance' || !category) {
       advisors = [...advisors, ...(await runPerformanceChecks(pool))];
     }
     
     return advisors.filter(a => !severity || a.severity === severity);
   }
   ```

3. **Integration with Existing Tools**:
   - Modify the tool registration system to use local implementations when in development mode
   - Ensure seamless switching between local and cloud implementations based on environment
   - Add configuration options to specify which implementation to use

4. **Error Handling and Fallbacks**:
   - Implement robust error handling for Docker communication failures
   - Add fallback mechanisms when local containers are not available
   - Provide clear error messages that guide developers on how to fix environment issues

# Test Strategy:
1. **Unit Testing**:
   - Write unit tests for both local implementations using Jest or similar testing framework
   - Mock Docker CLI responses for get_logs tests
   - Mock database responses for get_advisors tests
   - Test edge cases like empty logs, malformed log entries, and various filter combinations

2. **Integration Testing**:
   - Set up a test environment with local Supabase containers
   - Verify that get_logs correctly retrieves and formats logs from different Supabase services
   - Test get_advisors against a database with known issues to verify detection capabilities
   - Compare results between local and cloud implementations to ensure consistency

3. **Manual Testing Checklist**:
   - Start local Supabase instance: `supabase start`
   - Generate some database activity to create logs
   - Test get_logs with various parameters (container names, line counts, filters)
   - Create test cases for get_advisors:
     - Create a table without a primary key
     - Create a table with sensitive data but no RLS policies
     - Run queries that would benefit from missing indexes
   - Verify that all issues are correctly identified

4. **Environment Verification**:
   - Create a script that verifies the local environment is properly configured
   - Check that Docker is running and Supabase containers are accessible
   - Verify database connection parameters are correct
   - Run this verification before running the actual tools

5. **Documentation Testing**:
   - Review documentation for clarity and completeness
   - Ensure examples work as described
   - Verify that differences between local and cloud implementations are clearly documented
