{"name": "@supabase/mcp-server-supabase", "version": "0.5.0", "description": "MCP server for local self-hosted Supabase instances", "license": "Apache-2.0", "type": "module", "main": "dist/index.cjs", "types": "dist/index.d.ts", "sideEffects": false, "scripts": {"build": "tsup --clean", "prepublishOnly": "npm run build", "test": "vitest", "test:unit": "vitest --project unit", "test:e2e": "vitest --project e2e", "test:integration": "vitest --project integration", "test:coverage": "vitest --coverage", "dev:local": "tsx src/transports/stdio.ts"}, "files": ["dist/**/*"], "bin": {"mcp-server-supabase": "./dist/transports/stdio.js"}, "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./dist/index.cjs"}, "./platform": {"import": "./dist/platform/index.js", "types": "./dist/platform/index.d.ts", "default": "./dist/platform/index.cjs"}}, "dependencies": {"@modelcontextprotocol/sdk": "^1.11.0", "@supabase/mcp-utils": "0.2.1", "@supabase/supabase-js": "^2.45.0", "@types/pino": "^7.0.5", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "common-tags": "^1.8.2", "graphql": "^16.11.0", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "ws": "^8.18.0", "zod": "^3.24.1"}, "devDependencies": {"@ai-sdk/anthropic": "^1.2.9", "@deno/eszip": "^0.92.0", "@electric-sql/pglite": "^0.2.17", "@total-typescript/tsconfig": "^1.0.4", "@types/common-tags": "^1.8.4", "@types/node": "^22.8.6", "@types/ws": "^8.5.13", "@vitest/coverage-v8": "^2.1.9", "ai": "^4.3.4", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "msw": "^2.7.3", "nanoid": "^5.1.5", "prettier": "^3.3.3", "tsup": "^8.3.5", "tsx": "^4.19.2", "typescript": "^5.6.3", "vitest": "^2.1.9"}}