import { z } from 'zod';
import type { SupabasePlatform } from '../platform/types.js';
import { injectableTool } from './util.js';
import { contextLogger } from '../utils/logger.js';
import {
  tableSpecSchema,
  columnSelectionSchema,
  dataRecordSchema,
  whereClauseSchema,
  orderBySchema,
  paginationSchema,
  returningSchema,
  safeParseWithDetails,
  type WhereClause,
} from './crud-validation.js';

export type CrudToolsOptions = {
  platform: SupabasePlatform;
  projectId?: string;
  readOnly?: boolean;
};

/**
 * Enhanced validation schemas are now imported from crud-validation.ts
 * These provide comprehensive validation including:
 * - PostgreSQL identifier validation with reserved word checking
 * - Enhanced column value validation with size limits
 * - Comprehensive WHERE clause validation with logical operators
 * - Better error messaging and type safety
 */

export function getCrudTools({
  platform,
  projectId,
  readOnly,
}: CrudToolsOptions) {
  const logger = contextLogger.child({ component: 'CrudTools' });
  const project_id = projectId;

  /**
   * Helper function to escape SQL values for inline queries
   */
  function escapeSqlValue(value: any): string {
    if (value === null || value === undefined) {
      return 'NULL';
    }
    if (typeof value === 'string') {
      return `'${value.replace(/'/g, "''")}'`;
    }
    if (typeof value === 'number') {
      return String(value);
    }
    if (typeof value === 'boolean') {
      return value ? 'TRUE' : 'FALSE';
    }
    if (value instanceof Date) {
      return `'${value.toISOString()}'`;
    }
    if (Array.isArray(value) || typeof value === 'object') {
      return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
    }
    return `'${String(value).replace(/'/g, "''")}'`;
  }

  /**
   * Enhanced helper function to build WHERE clause SQL with inline values
   */
  function buildWhereClause(where: WhereClause): string {
    if ('AND' in where) {
      const conditions = where.AND.map(condition => buildWhereClause(condition));
      return `(${conditions.join(' AND ')})`;
    }

    if ('OR' in where) {
      const conditions = where.OR.map(condition => buildWhereClause(condition));
      return `(${conditions.join(' OR ')})`;
    }

    if ('NOT' in where) {
      const notCondition = buildWhereClause(where.NOT);
      return `NOT (${notCondition})`;
    }

    // Simple condition
    const condition = where as { column: string; operator: string; value?: any };

    switch (condition.operator) {
      case 'IS NULL':
        return `${quoteIdentifier(condition.column)} IS NULL`;
      case 'IS NOT NULL':
        return `${quoteIdentifier(condition.column)} IS NOT NULL`;
      case 'IN':
        if (!Array.isArray(condition.value)) {
          throw new Error('IN operator requires an array value');
        }
        const inValues = condition.value.map(v => escapeSqlValue(v)).join(', ');
        return `${quoteIdentifier(condition.column)} IN (${inValues})`;
      case 'NOT IN':
        if (!Array.isArray(condition.value)) {
          throw new Error('NOT IN operator requires an array value');
        }
        const notInValues = condition.value.map(v => escapeSqlValue(v)).join(', ');
        return `${quoteIdentifier(condition.column)} NOT IN (${notInValues})`;
      case 'BETWEEN':
        if (!Array.isArray(condition.value) || condition.value.length !== 2) {
          throw new Error('BETWEEN operator requires exactly two values');
        }
        return `${quoteIdentifier(condition.column)} BETWEEN ${escapeSqlValue(condition.value[0])} AND ${escapeSqlValue(condition.value[1])}`;
      case 'NOT BETWEEN':
        if (!Array.isArray(condition.value) || condition.value.length !== 2) {
          throw new Error('NOT BETWEEN operator requires exactly two values');
        }
        return `${quoteIdentifier(condition.column)} NOT BETWEEN ${escapeSqlValue(condition.value[0])} AND ${escapeSqlValue(condition.value[1])}`;
      default:
        return `${quoteIdentifier(condition.column)} ${condition.operator} ${escapeSqlValue(condition.value)}`;
    }
  }

  /**
   * Helper function to safely quote identifiers
   */
  function quoteIdentifier(identifier: string): string {
    return `"${identifier.replace(/"/g, '""')}"`;
  }

  const crudTools = {
    create_record: injectableTool({
      description: 'Insert a new record into a table.',
      parameters: z.object({
        project_id: z.string(),
        ...tableSpecSchema.shape,
        data: dataRecordSchema.describe('Column-value pairs for the new record'),
        returning: returningSchema.describe('Columns to return after insert (default: all)'),
      }),
      inject: { project_id },
      execute: async ({ project_id, schema, table, data, returning }) => {
        if (readOnly) {
          throw new Error('Cannot create records in read-only mode.');
        }

        // Enhanced validation using safe parsing
        const tableValidation = safeParseWithDetails(tableSpecSchema, { schema, table }, 'Table specification');
        if (!tableValidation.success) {
          throw new Error(tableValidation.error);
        }

        const dataValidation = safeParseWithDetails(dataRecordSchema, data, 'Data record');
        if (!dataValidation.success) {
          throw new Error(dataValidation.error);
        }

        if (returning) {
          const returningValidation = safeParseWithDetails(returningSchema, returning, 'Returning columns');
          if (!returningValidation.success) {
            throw new Error(returningValidation.error);
          }
        }

        try {
          const columns = Object.keys(data);
          const values = Object.values(data);

          const columnsSql = columns.map(col => quoteIdentifier(col)).join(', ');
          const valuesSql = values.map(val => escapeSqlValue(val)).join(', ');
          const tableName = `${quoteIdentifier(schema)}.${quoteIdentifier(table)}`;

          let returningSql = '';
          if (returning && returning.length > 0) {
            returningSql = ` RETURNING ${returning.map((col: string) => quoteIdentifier(col)).join(', ')}`;
          } else {
            returningSql = ' RETURNING *';
          }

          const query = `INSERT INTO ${tableName} (${columnsSql}) VALUES (${valuesSql})${returningSql}`;

          logger.info('Executing CREATE query', {
            table: `${schema}.${table}`,
            columns: columns.length
          });

          const result = await platform.executeSql(project_id, {
            query,
            read_only: false,
          });

          return {
            success: true,
            data: result,
            rowsAffected: result.length,
          };
        } catch (error) {
          logger.error(`CREATE operation failed for ${schema}.${table}: ${error instanceof Error ? error.message : String(error)}`);
          throw new Error(`Failed to create record: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    read_records: injectableTool({
      description: 'Read records from a table with optional filtering, sorting, and pagination.',
      parameters: z.object({
        project_id: z.string(),
        ...tableSpecSchema.shape,
        columns: columnSelectionSchema.optional().describe('Columns to select (default: all)'),
        where: whereClauseSchema.optional().describe('WHERE conditions'),
        orderBy: z.array(orderBySchema).optional().describe('ORDER BY clauses'),
        ...paginationSchema.shape,
      }),
      inject: { project_id },
      execute: async ({ project_id, schema, table, columns, where, orderBy, limit, offset }) => {
        // Enhanced validation using safe parsing
        const tableValidation = safeParseWithDetails(tableSpecSchema, { schema, table }, 'Table specification');
        if (!tableValidation.success) {
          throw new Error(tableValidation.error);
        }

        if (columns) {
          const columnsValidation = safeParseWithDetails(columnSelectionSchema, columns, 'Column selection');
          if (!columnsValidation.success) {
            throw new Error(columnsValidation.error);
          }
        }

        if (where) {
          const whereValidation = safeParseWithDetails(whereClauseSchema, where, 'WHERE clause');
          if (!whereValidation.success) {
            throw new Error(whereValidation.error);
          }
        }

        if (orderBy) {
          const orderByValidation = safeParseWithDetails(z.array(orderBySchema), orderBy, 'ORDER BY clause');
          if (!orderByValidation.success) {
            throw new Error(orderByValidation.error);
          }
        }

        const paginationValidation = safeParseWithDetails(paginationSchema, { limit, offset }, 'Pagination');
        if (!paginationValidation.success) {
          throw new Error(paginationValidation.error);
        }

        try {
          const tableName = `${quoteIdentifier(schema)}.${quoteIdentifier(table)}`;

          // Build SELECT clause
          let selectClause = '*';
          if (columns && columns.length > 0) {
            selectClause = columns.map(col => quoteIdentifier(col)).join(', ');
          }

          let query = `SELECT ${selectClause} FROM ${tableName}`;

          // Build WHERE clause
          if (where) {
            const whereSql = buildWhereClause(where);
            query += ` WHERE ${whereSql}`;
          }

          // Build ORDER BY clause
          if (orderBy && orderBy.length > 0) {
            const orderClauses = orderBy.map(order =>
              `${quoteIdentifier(order.column)} ${order.direction}`
            );
            query += ` ORDER BY ${orderClauses.join(', ')}`;
          }

          // Build LIMIT and OFFSET
          if (limit !== undefined) {
            query += ` LIMIT ${limit}`;
          }

          if (offset !== undefined) {
            query += ` OFFSET ${offset}`;
          }

          logger.info('Executing READ query', {
            table: `${schema}.${table}`,
            hasWhere: !!where,
            hasOrderBy: !!(orderBy && orderBy.length > 0),
            limit,
            offset
          });

          const result = await platform.executeSql(project_id, {
            query,
            read_only: true,
          });

          return {
            success: true,
            data: result,
            rowsReturned: result.length,
          };
        } catch (error) {
          logger.error(`READ operation failed for ${schema}.${table}: ${error instanceof Error ? error.message : String(error)}`);
          throw new Error(`Failed to read records: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    update_records: injectableTool({
      description: 'Update existing records in a table.',
      parameters: z.object({
        project_id: z.string(),
        ...tableSpecSchema.shape,
        data: dataRecordSchema.describe('Column-value pairs to update'),
        where: whereClauseSchema.describe('WHERE conditions to identify records to update'),
        returning: z.array(z.string()).optional().describe('Columns to return after update (default: all)'),
      }),
      inject: { project_id },
      execute: async ({ project_id, schema, table, data, where, returning }) => {
        if (readOnly) {
          throw new Error('Cannot update records in read-only mode.');
        }

        try {
          const updates = Object.keys(data);
          const values = Object.values(data);

          if (updates.length === 0) {
            throw new Error('No data provided for update');
          }

          const tableName = `${quoteIdentifier(schema)}.${quoteIdentifier(table)}`;

          // Build SET clause
          const setClause = updates.map((col, i) =>
            `${quoteIdentifier(col)} = ${escapeSqlValue(values[i])}`
          ).join(', ');

          let query = `UPDATE ${tableName} SET ${setClause}`;

          // Build WHERE clause
          const whereSql = buildWhereClause(where);
          query += ` WHERE ${whereSql}`;

          // Build RETURNING clause
          let returningSql = '';
          if (returning && returning.length > 0) {
            returningSql = ` RETURNING ${returning.map((col: string) => quoteIdentifier(col)).join(', ')}`;
          } else {
            returningSql = ' RETURNING *';
          }
          query += returningSql;

          logger.info('Executing UPDATE query', {
            table: `${schema}.${table}`,
            columns: updates.length,
            hasWhere: true
          });

          const result = await platform.executeSql(project_id, {
            query,
            read_only: false,
          });

          return {
            success: true,
            data: result,
            rowsAffected: result.length,
          };
        } catch (error) {
          logger.error(`UPDATE operation failed for ${schema}.${table}: ${error instanceof Error ? error.message : String(error)}`);
          throw new Error(`Failed to update records: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    delete_records: injectableTool({
      description: 'Delete records from a table.',
      parameters: z.object({
        project_id: z.string(),
        ...tableSpecSchema.shape,
        where: whereClauseSchema.describe('WHERE conditions to identify records to delete'),
        returning: z.array(z.string()).optional().describe('Columns to return from deleted records (default: all)'),
      }),
      inject: { project_id },
      execute: async ({ project_id, schema, table, where, returning }) => {
        if (readOnly) {
          throw new Error('Cannot delete records in read-only mode.');
        }

        try {
          const tableName = `${quoteIdentifier(schema)}.${quoteIdentifier(table)}`;

          let query = `DELETE FROM ${tableName}`;

          // Build WHERE clause
          const whereSql = buildWhereClause(where);
          query += ` WHERE ${whereSql}`;

          // Build RETURNING clause
          let returningSql = '';
          if (returning && returning.length > 0) {
            returningSql = ` RETURNING ${returning.map((col: string) => quoteIdentifier(col)).join(', ')}`;
          } else {
            returningSql = ' RETURNING *';
          }
          query += returningSql;

          logger.info('Executing DELETE query', {
            table: `${schema}.${table}`,
            hasWhere: true
          });

          const result = await platform.executeSql(project_id, {
            query,
            read_only: false,
          });

          return {
            success: true,
            data: result,
            rowsAffected: result.length,
          };
        } catch (error) {
          logger.error(`DELETE operation failed for ${schema}.${table}: ${error instanceof Error ? error.message : String(error)}`);
          throw new Error(`Failed to delete records: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    upsert_record: injectableTool({
      description: 'Insert a record or update it if it already exists (upsert operation).',
      parameters: z.object({
        project_id: z.string(),
        ...tableSpecSchema.shape,
        data: dataRecordSchema.describe('Column-value pairs for the record'),
        conflictColumns: z.array(z.string()).describe('Columns that define uniqueness for conflict resolution'),
        updateOnConflict: z.boolean().default(true).describe('Whether to update on conflict (default: true)'),
        returning: z.array(z.string()).optional().describe('Columns to return after upsert (default: all)'),
      }),
      inject: { project_id },
      execute: async ({ project_id, schema, table, data, conflictColumns, updateOnConflict, returning }) => {
        if (readOnly) {
          throw new Error('Cannot upsert records in read-only mode.');
        }

        try {
          const columns = Object.keys(data);
          const values = Object.values(data);
          
          if (columns.length === 0) {
            throw new Error('No data provided for upsert');
          }

          if (conflictColumns.length === 0) {
            throw new Error('Conflict columns must be specified for upsert');
          }

          const tableName = `${quoteIdentifier(schema)}.${quoteIdentifier(table)}`;
          const columnsSql = columns.map(col => quoteIdentifier(col)).join(', ');
          const valuesSql = values.map(val => escapeSqlValue(val)).join(', ');
          const conflictColumnsSql = conflictColumns.map((col: string) => quoteIdentifier(col)).join(', ');

          let query = `INSERT INTO ${tableName} (${columnsSql}) VALUES (${valuesSql})`;
          query += ` ON CONFLICT (${conflictColumnsSql})`;

          if (updateOnConflict) {
            // Build update set for conflict resolution, excluding conflict columns
            const updateColumns = columns.filter(col => !conflictColumns.includes(col));
            if (updateColumns.length > 0) {
              const updateSetSql = updateColumns.map(col =>
                `${quoteIdentifier(col)} = EXCLUDED.${quoteIdentifier(col)}`
              ).join(', ');
              query += ` DO UPDATE SET ${updateSetSql}`;
            } else {
              query += ' DO NOTHING';
            }
          } else {
            query += ' DO NOTHING';
          }

          // Build RETURNING clause
          let returningSql = '';
          if (returning && returning.length > 0) {
            returningSql = ` RETURNING ${returning.map((col: string) => quoteIdentifier(col)).join(', ')}`;
          } else {
            returningSql = ' RETURNING *';
          }
          query += returningSql;

          logger.info('Executing UPSERT query', {
            table: `${schema}.${table}`,
            columns: columns.length,
            conflictColumns: conflictColumns.length,
            updateOnConflict
          });

          const result = await platform.executeSql(project_id, {
            query,
            read_only: false,
          });

          return {
            success: true,
            data: result,
            rowsAffected: result.length,
          };
        } catch (error) {
          logger.error(`UPSERT operation failed for ${schema}.${table}: ${error instanceof Error ? error.message : String(error)}`);
          throw new Error(`Failed to upsert record: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),
  };

  return crudTools;
}