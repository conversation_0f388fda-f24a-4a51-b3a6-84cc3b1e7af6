import { z } from 'zod';
import type { SupabasePlatform } from '../platform/types.js';
import { injectableTool } from './util.js';
import { contextLogger } from '../utils/logger.js';
import {
  tableSpecSchema,
  columnSelectionSchema,
  dataRecordSchema,
  whereClauseSchema,
  orderBySchema,
  paginationSchema,
  returningSchema,
  safeParseWithDetails,
  type WhereClause,
} from './crud-validation.js';

export type CrudToolsOptions = {
  platform: SupabasePlatform;
  projectId?: string;
  readOnly?: boolean;
};

/**
 * Enhanced validation schemas are now imported from crud-validation.ts
 * These provide comprehensive validation including:
 * - PostgreSQL identifier validation with reserved word checking
 * - Enhanced column value validation with size limits
 * - Comprehensive WHERE clause validation with logical operators
 * - Better error messaging and type safety
 */

export function getCrudTools({
  platform,
  projectId,
  readOnly,
}: CrudToolsOptions) {
  const logger = contextLogger.child({ component: 'CrudTools' });
  const project_id = projectId;

  /**
   * Enhanced helper function to build WHERE clause SQL with support for all operators
   */
  function buildWhereClause(where: WhereClause, params: any[] = []): { sql: string; params: any[] } {
    if ('AND' in where) {
      const conditions = where.AND.map(condition => buildWhereClause(condition, params));
      return {
        sql: `(${conditions.map(c => c.sql).join(' AND ')})`,
        params
      };
    }
    
    if ('OR' in where) {
      const conditions = where.OR.map(condition => buildWhereClause(condition, params));
      return {
        sql: `(${conditions.map(c => c.sql).join(' OR ')})`,
        params
      };
    }

    if ('NOT' in where) {
      const notCondition = buildWhereClause(where.NOT, params);
      return {
        sql: `NOT (${notCondition.sql})`,
        params: notCondition.params
      };
    }

    // Simple condition
    const condition = where as { column: string; operator: string; value?: any };
    const paramIndex = params.length + 1;

    switch (condition.operator) {
      case 'IS NULL':
        return { sql: `${quoteIdentifier(condition.column)} IS NULL`, params };
      case 'IS NOT NULL':
        return { sql: `${quoteIdentifier(condition.column)} IS NOT NULL`, params };
      case 'IN':
        if (!Array.isArray(condition.value)) {
          throw new Error('IN operator requires an array value');
        }
        const inParams = condition.value.map((_, i) => `$${paramIndex + i}`).join(', ');
        params.push(...condition.value);
        return { sql: `${quoteIdentifier(condition.column)} IN (${inParams})`, params };
      case 'NOT IN':
        if (!Array.isArray(condition.value)) {
          throw new Error('NOT IN operator requires an array value');
        }
        const notInParams = condition.value.map((_, i) => `$${paramIndex + i}`).join(', ');
        params.push(...condition.value);
        return { sql: `${quoteIdentifier(condition.column)} NOT IN (${notInParams})`, params };
      case 'BETWEEN':
        if (!Array.isArray(condition.value) || condition.value.length !== 2) {
          throw new Error('BETWEEN operator requires exactly two values');
        }
        params.push(condition.value[0], condition.value[1]);
        return { 
          sql: `${quoteIdentifier(condition.column)} BETWEEN $${paramIndex} AND $${paramIndex + 1}`, 
          params 
        };
      case 'NOT BETWEEN':
        if (!Array.isArray(condition.value) || condition.value.length !== 2) {
          throw new Error('NOT BETWEEN operator requires exactly two values');
        }
        params.push(condition.value[0], condition.value[1]);
        return { 
          sql: `${quoteIdentifier(condition.column)} NOT BETWEEN $${paramIndex} AND $${paramIndex + 1}`, 
          params 
        };
      default:
        params.push(condition.value);
        return { sql: `${quoteIdentifier(condition.column)} ${condition.operator} $${paramIndex}`, params };
    }
  }

  /**
   * Helper function to safely quote identifiers
   */
  function quoteIdentifier(identifier: string): string {
    return `"${identifier.replace(/"/g, '""')}"`;
  }

  const crudTools = {
    create_record: injectableTool({
      description: 'Insert a new record into a table.',
      parameters: z.object({
        project_id: z.string(),
        ...tableSpecSchema.shape,
        data: dataRecordSchema.describe('Column-value pairs for the new record'),
        returning: returningSchema.describe('Columns to return after insert (default: all)'),
      }),
      inject: { project_id },
      execute: async ({ project_id, schema, table, data, returning }) => {
        if (readOnly) {
          throw new Error('Cannot create records in read-only mode.');
        }

        // Enhanced validation using safe parsing
        const tableValidation = safeParseWithDetails(tableSpecSchema, { schema, table }, 'Table specification');
        if (!tableValidation.success) {
          throw new Error(tableValidation.error);
        }

        const dataValidation = safeParseWithDetails(dataRecordSchema, data, 'Data record');
        if (!dataValidation.success) {
          throw new Error(dataValidation.error);
        }

        if (returning) {
          const returningValidation = safeParseWithDetails(returningSchema, returning, 'Returning columns');
          if (!returningValidation.success) {
            throw new Error(returningValidation.error);
          }
        }

        try {
          const columns = Object.keys(data);
          const values = Object.values(data);

          const columnsSql = columns.map(col => quoteIdentifier(col)).join(', ');
          const parametersSql = values.map((_, i) => `$${i + 1}`).join(', ');
          const tableName = `${quoteIdentifier(schema)}.${quoteIdentifier(table)}`;
          
          let returningSql = '';
          if (returning && returning.length > 0) {
            returningSql = ` RETURNING ${returning.map(col => quoteIdentifier(col)).join(', ')}`;
          } else {
            returningSql = ' RETURNING *';
          }

          const query = `INSERT INTO ${tableName} (${columnsSql}) VALUES (${parametersSql})${returningSql}`;
          
          logger.info('Executing CREATE query', { 
            table: `${schema}.${table}`, 
            columns: columns.length,
            query: query.replace(/\$\d+/g, '?') // Log query with placeholders
          });

          const result = await platform.executeSql(project_id, {
            query,
            params: values,
            read_only: false,
          });

          return {
            success: true,
            data: result,
            rowsAffected: result.length,
          };
        } catch (error) {
          logger.error('CREATE operation failed', { 
            table: `${schema}.${table}`, 
            error: error instanceof Error ? error.message : String(error) 
          });
          throw new Error(`Failed to create record: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    read_records: injectableTool({
      description: 'Read records from a table with optional filtering, sorting, and pagination.',
      parameters: z.object({
        project_id: z.string(),
        ...tableSpecSchema.shape,
        columns: columnSelectionSchema.optional().describe('Columns to select (default: all)'),
        where: whereClauseSchema.optional().describe('WHERE conditions'),
        orderBy: z.array(orderBySchema).optional().describe('ORDER BY clauses'),
        ...paginationSchema.shape,
      }),
      inject: { project_id },
      execute: async ({ project_id, schema, table, columns, where, orderBy, limit, offset }) => {
        // Enhanced validation using safe parsing
        const tableValidation = safeParseWithDetails(tableSpecSchema, { schema, table }, 'Table specification');
        if (!tableValidation.success) {
          throw new Error(tableValidation.error);
        }

        if (columns) {
          const columnsValidation = safeParseWithDetails(columnSelectionSchema, columns, 'Column selection');
          if (!columnsValidation.success) {
            throw new Error(columnsValidation.error);
          }
        }

        if (where) {
          const whereValidation = safeParseWithDetails(whereClauseSchema, where, 'WHERE clause');
          if (!whereValidation.success) {
            throw new Error(whereValidation.error);
          }
        }

        if (orderBy) {
          const orderByValidation = safeParseWithDetails(z.array(orderBySchema), orderBy, 'ORDER BY clause');
          if (!orderByValidation.success) {
            throw new Error(orderByValidation.error);
          }
        }

        const paginationValidation = safeParseWithDetails(paginationSchema, { limit, offset }, 'Pagination');
        if (!paginationValidation.success) {
          throw new Error(paginationValidation.error);
        }

        try {
          let params: any[] = [];
          const tableName = `${quoteIdentifier(schema)}.${quoteIdentifier(table)}`;
          
          // Build SELECT clause
          let selectClause = '*';
          if (columns && columns.length > 0) {
            selectClause = columns.map(col => quoteIdentifier(col)).join(', ');
          }

          let query = `SELECT ${selectClause} FROM ${tableName}`;

          // Build WHERE clause
          if (where) {
            const whereResult = buildWhereClause(where, params);
            query += ` WHERE ${whereResult.sql}`;
            params = whereResult.params;
          }

          // Build ORDER BY clause
          if (orderBy && orderBy.length > 0) {
            const orderClauses = orderBy.map(order => 
              `${quoteIdentifier(order.column)} ${order.direction}`
            );
            query += ` ORDER BY ${orderClauses.join(', ')}`;
          }

          // Build LIMIT and OFFSET
          if (limit !== undefined) {
            query += ` LIMIT $${params.length + 1}`;
            params.push(limit);
          }

          if (offset !== undefined) {
            query += ` OFFSET $${params.length + 1}`;
            params.push(offset);
          }

          logger.info('Executing READ query', { 
            table: `${schema}.${table}`, 
            hasWhere: !!where,
            hasOrderBy: !!(orderBy && orderBy.length > 0),
            limit,
            offset
          });

          const result = await platform.executeSql(project_id, {
            query,
            params,
            read_only: true,
          });

          return {
            success: true,
            data: result,
            rowsReturned: result.length,
          };
        } catch (error) {
          logger.error('READ operation failed', { 
            table: `${schema}.${table}`, 
            error: error instanceof Error ? error.message : String(error) 
          });
          throw new Error(`Failed to read records: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    update_records: injectableTool({
      description: 'Update existing records in a table.',
      parameters: z.object({
        project_id: z.string(),
        ...tableSchema.shape,
        data: z.record(z.string(), columnValueSchema).describe('Column-value pairs to update'),
        where: whereClauseSchema.describe('WHERE conditions to identify records to update'),
        returning: z.array(z.string()).optional().describe('Columns to return after update (default: all)'),
      }),
      inject: { project_id },
      execute: async ({ project_id, schema, table, data, where, returning }) => {
        if (readOnly) {
          throw new Error('Cannot update records in read-only mode.');
        }

        try {
          const updates = Object.keys(data);
          const values = Object.values(data);
          
          if (updates.length === 0) {
            throw new Error('No data provided for update');
          }

          let params = [...values];
          const tableName = `${quoteIdentifier(schema)}.${quoteIdentifier(table)}`;
          
          // Build SET clause
          const setClause = updates.map((col, i) => 
            `${quoteIdentifier(col)} = $${i + 1}`
          ).join(', ');

          let query = `UPDATE ${tableName} SET ${setClause}`;

          // Build WHERE clause
          const whereResult = buildWhereClause(where, params);
          query += ` WHERE ${whereResult.sql}`;
          params = whereResult.params;

          // Build RETURNING clause
          let returningSql = '';
          if (returning && returning.length > 0) {
            returningSql = ` RETURNING ${returning.map(col => quoteIdentifier(col)).join(', ')}`;
          } else {
            returningSql = ' RETURNING *';
          }
          query += returningSql;

          logger.info('Executing UPDATE query', { 
            table: `${schema}.${table}`, 
            columns: updates.length,
            hasWhere: true
          });

          const result = await platform.executeSql(project_id, {
            query,
            params,
            read_only: false,
          });

          return {
            success: true,
            data: result,
            rowsAffected: result.length,
          };
        } catch (error) {
          logger.error('UPDATE operation failed', { 
            table: `${schema}.${table}`, 
            error: error instanceof Error ? error.message : String(error) 
          });
          throw new Error(`Failed to update records: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    delete_records: injectableTool({
      description: 'Delete records from a table.',
      parameters: z.object({
        project_id: z.string(),
        ...tableSchema.shape,
        where: whereClauseSchema.describe('WHERE conditions to identify records to delete'),
        returning: z.array(z.string()).optional().describe('Columns to return from deleted records (default: all)'),
      }),
      inject: { project_id },
      execute: async ({ project_id, schema, table, where, returning }) => {
        if (readOnly) {
          throw new Error('Cannot delete records in read-only mode.');
        }

        try {
          let params: any[] = [];
          const tableName = `${quoteIdentifier(schema)}.${quoteIdentifier(table)}`;
          
          let query = `DELETE FROM ${tableName}`;

          // Build WHERE clause
          const whereResult = buildWhereClause(where, params);
          query += ` WHERE ${whereResult.sql}`;
          params = whereResult.params;

          // Build RETURNING clause
          let returningSql = '';
          if (returning && returning.length > 0) {
            returningSql = ` RETURNING ${returning.map(col => quoteIdentifier(col)).join(', ')}`;
          } else {
            returningSql = ' RETURNING *';
          }
          query += returningSql;

          logger.info('Executing DELETE query', { 
            table: `${schema}.${table}`, 
            hasWhere: true
          });

          const result = await platform.executeSql(project_id, {
            query,
            params,
            read_only: false,
          });

          return {
            success: true,
            data: result,
            rowsAffected: result.length,
          };
        } catch (error) {
          logger.error('DELETE operation failed', { 
            table: `${schema}.${table}`, 
            error: error instanceof Error ? error.message : String(error) 
          });
          throw new Error(`Failed to delete records: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),

    upsert_record: injectableTool({
      description: 'Insert a record or update it if it already exists (upsert operation).',
      parameters: z.object({
        project_id: z.string(),
        ...tableSchema.shape,
        data: z.record(z.string(), columnValueSchema).describe('Column-value pairs for the record'),
        conflictColumns: z.array(z.string()).describe('Columns that define uniqueness for conflict resolution'),
        updateOnConflict: z.boolean().default(true).describe('Whether to update on conflict (default: true)'),
        returning: z.array(z.string()).optional().describe('Columns to return after upsert (default: all)'),
      }),
      inject: { project_id },
      execute: async ({ project_id, schema, table, data, conflictColumns, updateOnConflict, returning }) => {
        if (readOnly) {
          throw new Error('Cannot upsert records in read-only mode.');
        }

        try {
          const columns = Object.keys(data);
          const values = Object.values(data);
          
          if (columns.length === 0) {
            throw new Error('No data provided for upsert');
          }

          if (conflictColumns.length === 0) {
            throw new Error('Conflict columns must be specified for upsert');
          }

          const tableName = `${quoteIdentifier(schema)}.${quoteIdentifier(table)}`;
          const columnsSql = columns.map(col => quoteIdentifier(col)).join(', ');
          const parametersSql = values.map((_, i) => `$${i + 1}`).join(', ');
          const conflictColumnsSql = conflictColumns.map(col => quoteIdentifier(col)).join(', ');

          let query = `INSERT INTO ${tableName} (${columnsSql}) VALUES (${parametersSql})`;
          query += ` ON CONFLICT (${conflictColumnsSql})`;

          if (updateOnConflict) {
            // Build update set for conflict resolution, excluding conflict columns
            const updateColumns = columns.filter(col => !conflictColumns.includes(col));
            if (updateColumns.length > 0) {
              const updateSetSql = updateColumns.map(col => 
                `${quoteIdentifier(col)} = EXCLUDED.${quoteIdentifier(col)}`
              ).join(', ');
              query += ` DO UPDATE SET ${updateSetSql}`;
            } else {
              query += ' DO NOTHING';
            }
          } else {
            query += ' DO NOTHING';
          }

          // Build RETURNING clause
          let returningSql = '';
          if (returning && returning.length > 0) {
            returningSql = ` RETURNING ${returning.map(col => quoteIdentifier(col)).join(', ')}`;
          } else {
            returningSql = ' RETURNING *';
          }
          query += returningSql;

          logger.info('Executing UPSERT query', { 
            table: `${schema}.${table}`, 
            columns: columns.length,
            conflictColumns: conflictColumns.length,
            updateOnConflict
          });

          const result = await platform.executeSql(project_id, {
            query,
            params: values,
            read_only: false,
          });

          return {
            success: true,
            data: result,
            rowsAffected: result.length,
          };
        } catch (error) {
          logger.error('UPSERT operation failed', { 
            table: `${schema}.${table}`, 
            error: error instanceof Error ? error.message : String(error) 
          });
          throw new Error(`Failed to upsert record: ${error instanceof Error ? error.message : String(error)}`);
        }
      },
    }),
  };

  return crudTools;
}