import { z } from 'zod';
import { listExtensionsSql, listTablesSql } from '../pg-meta/index.js';
import {
  postgresExtensionSchema,
  postgresTableSchema,
} from '../pg-meta/types.js';
import type { SupabasePlatform } from '../platform/types.js';
import { injectableTool } from './util.js';

export type DatabaseOperationToolsOptions = {
  platform: SupabasePlatform;
  projectId?: string;
  readOnly?: boolean;
};

export function getDatabaseOperationTools({
  platform,
  projectId,
  readOnly,
}: DatabaseOperationToolsOptions) {
  const project_id = projectId;

  const databaseOperationTools = {
    list_tables: injectableTool({
      description: 'Lists all tables in one or more schemas.',
      parameters: z.object({
        project_id: z.string(),
        schemas: z
          .array(z.string())
          .describe('List of schemas to include. Defaults to all schemas.')
          .default(['public']),
      }),
      inject: { project_id },
      execute: async ({ project_id, schemas }) => {
        try {
          // Simplified query to avoid complex permission checks that might fail
          const schemaFilter = schemas.map(s => `'${s}'`).join(',');
          const query = `
            SELECT 
              c.oid::bigint as id,
              nc.nspname as schema,
              c.relname as name,
              COALESCE(c.relrowsecurity, false) as rls_enabled,
              COALESCE(c.relforcerowsecurity, false) as rls_forced,
              CASE COALESCE(c.relreplident, 'd')
                WHEN 'd' THEN 'DEFAULT'
                WHEN 'n' THEN 'NOTHING' 
                WHEN 'f' THEN 'FULL'
                WHEN 'i' THEN 'INDEX'
                ELSE 'DEFAULT'
              END as replica_identity,
              COALESCE(pg_total_relation_size(c.oid), 0) as bytes,
              COALESCE(pg_size_pretty(pg_total_relation_size(c.oid)), '0 bytes') as size,
              COALESCE(pg_stat_get_live_tuples(c.oid), 0) as live_rows_estimate,
              COALESCE(pg_stat_get_dead_tuples(c.oid), 0) as dead_rows_estimate,
              COALESCE(obj_description(c.oid), '') as comment,
              '[]'::json as primary_keys,
              '[]'::json as relationships
            FROM pg_class c
            JOIN pg_namespace nc ON c.relnamespace = nc.oid
            WHERE c.relkind IN ('r', 'p')
              AND nc.nspname IN (${schemaFilter})
            ORDER BY nc.nspname, c.relname
          `;

          const data = await platform.executeSql(project_id, {
            query,
            read_only: readOnly,
          });

          // Parse and validate each table result
          const tables = data.map((table: any) => {
            try {
              // Ensure arrays are properly formatted
              const processedTable = {
                ...table,
                primary_keys: Array.isArray(table.primary_keys) ? table.primary_keys : [],
                relationships: Array.isArray(table.relationships) ? table.relationships : [],
                comment: table.comment || ''
              };
              
              return postgresTableSchema.parse(processedTable);
            } catch (parseError) {
              // Log parsing error for debugging but continue with fallback
              // Return a minimal valid table object if parsing fails
              return {
                id: Number(table.id) || 0,
                schema: String(table.schema) || 'public',
                name: String(table.name) || 'unknown_table',
                rls_enabled: Boolean(table.rls_enabled),
                rls_forced: Boolean(table.rls_forced),
                replica_identity: table.replica_identity || 'DEFAULT',
                bytes: Number(table.bytes) || 0,
                size: String(table.size) || '0 bytes',
                live_rows_estimate: Number(table.live_rows_estimate) || 0,
                dead_rows_estimate: Number(table.dead_rows_estimate) || 0,
                comment: String(table.comment || ''),
                primary_keys: [],
                relationships: []
              };
            }
          });

          return tables;
        } catch (error) {
          // Handle list_tables execution error with fallback query
          
          // Fallback to basic table listing if complex query fails
          try {
            const schemaFilter = schemas.map(s => `'${s}'`).join(',');
            const fallbackQuery = `
              SELECT 
                c.oid::bigint as id,
                nc.nspname as schema,
                c.relname as name,
                false as rls_enabled,
                false as rls_forced,
                'DEFAULT' as replica_identity,
                0 as bytes,
                '0 bytes' as size,
                0 as live_rows_estimate,
                0 as dead_rows_estimate,
                '' as comment,
                '[]'::json as primary_keys,
                '[]'::json as relationships
              FROM pg_class c
              JOIN pg_namespace nc ON c.relnamespace = nc.oid
              WHERE c.relkind IN ('r', 'p')
                AND nc.nspname IN (${schemaFilter})
              ORDER BY nc.nspname, c.relname
            `;
            
            const fallbackData = await platform.executeSql(project_id, {
              query: fallbackQuery,
              read_only: readOnly,
            });
            
            return fallbackData.map((table: any) => postgresTableSchema.parse({
              ...table,
              primary_keys: [],
              relationships: []
            }));
          } catch (fallbackError) {
            // Both primary and fallback queries failed
            throw new Error(`Failed to list tables: ${error instanceof Error ? error.message : String(error)}. Fallback also failed: ${fallbackError instanceof Error ? fallbackError.message : String(fallbackError)}`);
          }
        }
      },
    }),
    list_extensions: injectableTool({
      description: 'Lists all extensions in the database.',
      parameters: z.object({
        project_id: z.string(),
      }),
      inject: { project_id },
      execute: async ({ project_id }) => {
        const query = listExtensionsSql();
        const data = await platform.executeSql(project_id, {
          query,
          read_only: readOnly,
        });
        const extensions = data.map((extension) =>
          postgresExtensionSchema.parse(extension)
        );
        return extensions;
      },
    }),
    list_migrations: injectableTool({
      description: 'Lists all migrations in the database.',
      parameters: z.object({
        project_id: z.string(),
      }),
      inject: { project_id },
      execute: async ({ project_id }) => {
        return await platform.listMigrations(project_id);
      },
    }),
    apply_migration: injectableTool({
      description:
        'Applies a migration to the database. Use this when executing DDL operations. Do not hardcode references to generated IDs in data migrations.',
      parameters: z.object({
        project_id: z.string(),
        name: z.string().describe('The name of the migration in snake_case'),
        query: z.string().describe('The SQL query to apply'),
      }),
      inject: { project_id },
      execute: async ({ project_id, name, query }) => {
        if (readOnly) {
          throw new Error('Cannot apply migration in read-only mode.');
        }

        return await platform.applyMigration(project_id, {
          name,
          query,
        });
      },
    }),
    execute_sql: injectableTool({
      description:
        'Executes raw SQL in the Postgres database. Use `apply_migration` instead for DDL operations.',
      parameters: z.object({
        project_id: z.string(),
        query: z.string().describe('The SQL query to execute'),
      }),
      inject: { project_id },
      execute: async ({ query, project_id }) => {
        return await platform.executeSql(project_id, {
          query,
          read_only: readOnly,
        });
      },
    }),
  };

  return databaseOperationTools;
}
