import { z } from 'zod';

/**
 * Enhanced validation schemas for CRUD operations using zod v3+
 */

/**
 * PostgreSQL identifier validation (table names, column names, etc.)
 */
export const postgresIdentifierSchema = z
  .string()
  .min(1, 'Identifier cannot be empty')
  .max(63, 'PostgreSQL identifier cannot exceed 63 characters')
  .regex(/^[a-zA-Z_][a-zA-Z0-9_]*$/, 'Invalid identifier format')
  .refine((val) => !POSTGRES_RESERVED_WORDS.includes(val.toUpperCase()), {
    message: 'Cannot use PostgreSQL reserved word as identifier',
  });

/**
 * Schema name validation with additional constraints
 */
export const schemaNameSchema = postgresIdentifierSchema
  .refine((val) => val !== 'information_schema', {
    message: 'Cannot use information_schema as schema name',
  })
  .refine((val) => !val.startsWith('pg_'), {
    message: 'Cannot use PostgreSQL system schema names starting with pg_',
  });

/**
 * Table name validation
 */
export const tableNameSchema = postgresIdentifierSchema;

/**
 * Column name validation
 */
export const columnNameSchema = postgresIdentifierSchema;

/**
 * Enhanced column value schema with better type validation
 */
export const columnValueSchema = z.union([
  z.string().max(1048576, 'String value too large'), // 1MB limit
  z.number().finite('Number must be finite'),
  z.boolean(),
  z.null(),
  z.record(z.unknown()).refine((val) => {
    try {
      JSON.stringify(val);
      return true;
    } catch {
      return false;
    }
  }, 'Value must be JSON serializable'),
  z.array(z.unknown()).max(10000, 'Array too large').refine((val) => {
    try {
      JSON.stringify(val);
      return true;
    } catch {
      return false;
    }
  }, 'Array values must be JSON serializable'),
]);

/**
 * WHERE condition operators with validation
 */
export const whereOperatorSchema = z.enum([
  '=', '!=', '<>', '>', '<', '>=', '<=', 
  'LIKE', 'ILIKE', 'NOT LIKE', 'NOT ILIKE',
  'IS NULL', 'IS NOT NULL', 
  'IN', 'NOT IN',
  'BETWEEN', 'NOT BETWEEN',
  '~', '!~', '~*', '!~*', // Regular expression operators
  '@>', '<@', '?', '?&', '?|', // JSON operators
]);

/**
 * Enhanced WHERE condition schema
 */
export const whereConditionSchema = z.object({
  column: columnNameSchema,
  operator: whereOperatorSchema,
  value: z.union([
    columnValueSchema,
    z.array(columnValueSchema).min(1, 'IN/NOT IN requires at least one value'),
    z.tuple([columnValueSchema, columnValueSchema]).transform(([a, b]) => [a, b]), // For BETWEEN
  ]).optional(),
}).superRefine((data, ctx) => {
  const { operator, value } = data;
  
  // Validate operator-value combinations
  if (['IS NULL', 'IS NOT NULL'].includes(operator) && value !== undefined) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: `${operator} operator should not have a value`,
      path: ['value'],
    });
  }
  
  if (!['IS NULL', 'IS NOT NULL'].includes(operator) && value === undefined) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: `${operator} operator requires a value`,
      path: ['value'],
    });
  }
  
  if (['IN', 'NOT IN'].includes(operator) && !Array.isArray(value)) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: `${operator} operator requires an array value`,
      path: ['value'],
    });
  }
  
  if (['BETWEEN', 'NOT BETWEEN'].includes(operator) && (!Array.isArray(value) || value.length !== 2)) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: `${operator} operator requires exactly two values`,
      path: ['value'],
    });
  }
});

/**
 * Recursive WHERE clause schema with logical operators
 */
export const whereClauseSchema: z.ZodType<WhereClause> = z.lazy(() =>
  z.union([
    whereConditionSchema,
    z.object({
      AND: z.array(whereClauseSchema).min(1, 'AND requires at least one condition'),
    }),
    z.object({
      OR: z.array(whereClauseSchema).min(1, 'OR requires at least one condition'),
    }),
    z.object({
      NOT: whereClauseSchema,
    }),
  ])
);

export type WhereClause = 
  | z.infer<typeof whereConditionSchema>
  | { AND: WhereClause[] }
  | { OR: WhereClause[] }
  | { NOT: WhereClause };

/**
 * ORDER BY direction with validation
 */
export const orderDirectionSchema = z.enum(['ASC', 'DESC', 'asc', 'desc'])
  .transform((val) => val.toUpperCase() as 'ASC' | 'DESC');

/**
 * Enhanced ORDER BY schema
 */
export const orderBySchema = z.object({
  column: columnNameSchema,
  direction: orderDirectionSchema.default('ASC'),
  nulls: z.enum(['FIRST', 'LAST', 'first', 'last'])
    .transform((val) => val.toUpperCase() as 'FIRST' | 'LAST')
    .optional(),
});

/**
 * Pagination schema with enhanced validation
 */
export const paginationSchema = z.object({
  limit: z.number()
    .int('Limit must be an integer')
    .positive('Limit must be positive')
    .max(10000, 'Limit cannot exceed 10,000 records')
    .optional(),
  offset: z.number()
    .int('Offset must be an integer')
    .min(0, 'Offset cannot be negative')
    .max(1000000, 'Offset cannot exceed 1,000,000')
    .optional(),
});

/**
 * Table specification schema
 */
export const tableSpecSchema = z.object({
  schema: schemaNameSchema.default('public'),
  table: tableNameSchema,
});

/**
 * Column selection schema
 */
export const columnSelectionSchema = z
  .array(columnNameSchema)
  .min(1, 'Must select at least one column')
  .max(100, 'Cannot select more than 100 columns')
  .refine((columns) => {
    const uniqueColumns = new Set(columns);
    return uniqueColumns.size === columns.length;
  }, 'Column names must be unique');

/**
 * Data record schema for inserts/updates
 */
export const dataRecordSchema = z
  .record(columnNameSchema, columnValueSchema)
  .refine((data) => Object.keys(data).length > 0, 'Data cannot be empty')
  .refine((data) => Object.keys(data).length <= 100, 'Cannot have more than 100 columns');

/**
 * Conflict columns schema for upserts
 */
export const conflictColumnsSchema = z
  .array(columnNameSchema)
  .min(1, 'Must specify at least one conflict column')
  .max(10, 'Cannot have more than 10 conflict columns')
  .refine((columns) => {
    const uniqueColumns = new Set(columns);
    return uniqueColumns.size === columns.length;
  }, 'Conflict column names must be unique');

/**
 * RETURNING clause schema
 */
export const returningSchema = columnSelectionSchema.optional();

/**
 * PostgreSQL reserved words (subset of commonly used ones)
 */
const POSTGRES_RESERVED_WORDS = [
  'ALL', 'ANALYSE', 'ANALYZE', 'AND', 'ANY', 'ARRAY', 'AS', 'ASC', 'ASYMMETRIC',
  'AUTHORIZATION', 'BINARY', 'BOTH', 'CASE', 'CAST', 'CHECK', 'COLLATE', 'COLLATION',
  'COLUMN', 'CONCURRENTLY', 'CONSTRAINT', 'CREATE', 'CROSS', 'CURRENT_CATALOG',
  'CURRENT_DATE', 'CURRENT_ROLE', 'CURRENT_SCHEMA', 'CURRENT_TIME', 'CURRENT_TIMESTAMP',
  'CURRENT_USER', 'DEFAULT', 'DEFERRABLE', 'DESC', 'DISTINCT', 'DO', 'ELSE', 'END',
  'EXCEPT', 'FALSE', 'FETCH', 'FOR', 'FOREIGN', 'FREEZE', 'FROM', 'FULL', 'GRANT',
  'GROUP', 'HAVING', 'ILIKE', 'IN', 'INITIALLY', 'INNER', 'INTERSECT', 'INTO', 'IS',
  'ISNULL', 'JOIN', 'LATERAL', 'LEADING', 'LEFT', 'LIKE', 'LIMIT', 'LOCALTIME',
  'LOCALTIMESTAMP', 'NATURAL', 'NOT', 'NOTNULL', 'NULL', 'OFFSET', 'ON', 'ONLY',
  'OR', 'ORDER', 'OUTER', 'OVERLAPS', 'PLACING', 'PRIMARY', 'REFERENCES', 'RETURNING',
  'RIGHT', 'SELECT', 'SESSION_USER', 'SIMILAR', 'SOME', 'SYMMETRIC', 'TABLE', 'TABLESAMPLE',
  'THEN', 'TO', 'TRAILING', 'TRUE', 'UNION', 'UNIQUE', 'USER', 'USING', 'VARIADIC',
  'VERBOSE', 'WHEN', 'WHERE', 'WINDOW', 'WITH',
];

/**
 * Validation error formatter
 */
export function formatValidationErrors(error: z.ZodError): string {
  return error.errors
    .map((err) => {
      const path = err.path.length > 0 ? `${err.path.join('.')}: ` : '';
      return `${path}${err.message}`;
    })
    .join('; ');
}

/**
 * Safe parsing with detailed error messages
 */
export function safeParseWithDetails<T>(
  schema: z.ZodSchema<T>,
  data: unknown,
  context: string
): { success: true; data: T } | { success: false; error: string } {
  const result = schema.safeParse(data);
  
  if (result.success) {
    return { success: true, data: result.data };
  }
  
  const errorMessage = `${context} validation failed: ${formatValidationErrors(result.error)}`;
  return { success: false, error: errorMessage };
}