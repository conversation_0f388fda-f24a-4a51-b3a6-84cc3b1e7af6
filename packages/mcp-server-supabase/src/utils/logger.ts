import type { Logger, LoggerOptions } from 'pino';
const pino = require('pino');
import { randomUUID } from 'crypto';

export type LogLevel = 'trace' | 'debug' | 'info' | 'warn' | 'error' | 'fatal';

export interface LogContext {
  requestId?: string;
  toolName?: string;
  userId?: string;
  projectId?: string;
  operation?: string;
  duration?: number;
  [key: string]: unknown;
}

export interface LoggerConfig {
  level: LogLevel;
  pretty?: boolean;
  redact?: string[];
  destination?: string;
}

/**
 * Creates a structured logger instance with MCP-specific configuration
 */
export function createLogger(config: LoggerConfig = { level: 'info' }): Logger {
  const pinoConfig: LoggerOptions = {
    level: config.level,
    timestamp: pino.stdTimeFunctions.isoTime,
    formatters: {
      level: (label) => ({ level: label }),
    },
    redact: {
      paths: config.redact || [
        'password',
        'token',
        'apiKey',
        'serviceRoleKey',
        'anonKey',
        'authorization',
        'cookie',
        'secret',
      ],
      censor: '[REDACTED]',
    },
    serializers: {
      error: pino.stdSerializers.err,
      req: pino.stdSerializers.req,
      res: pino.stdSerializers.res,
    },
  };

  // Add pretty printing for development
  const transport = config.pretty
    ? pino.transport({
        target: 'pino-pretty',
        options: {
          colorize: true,
          translateTime: 'SYS:standard',
          ignore: 'pid,hostname',
          messageFormat: '{requestId} [{context}] {msg}',
        },
      })
    : undefined;

  return pino(pinoConfig, transport);
}

/**
 * Default logger instance
 */
export const logger = createLogger({
  level: (process.env.LOG_LEVEL as LogLevel) || 'info',
  pretty: process.env.NODE_ENV !== 'production',
});

/**
 * Request ID middleware for tracking requests across the application
 */
export class RequestTracker {
  private static requestStore = new Map<string, LogContext>();

  static generateRequestId(): string {
    return randomUUID();
  }

  static setContext(requestId: string, context: LogContext) {
    this.requestStore.set(requestId, { ...context, requestId });
  }

  static getContext(requestId: string): LogContext | undefined {
    return this.requestStore.get(requestId);
  }

  static updateContext(requestId: string, updates: Partial<LogContext>) {
    const existing = this.requestStore.get(requestId) || {};
    this.requestStore.set(requestId, { ...existing, ...updates, requestId });
  }

  static clearContext(requestId: string) {
    this.requestStore.delete(requestId);
  }

  static cleanup() {
    // Clean up old request contexts (older than 1 hour)
    const oneHourAgo = Date.now() - 60 * 60 * 1000;
    for (const [requestId, context] of this.requestStore.entries()) {
      if (context.timestamp && typeof context.timestamp === 'number' && context.timestamp < oneHourAgo) {
        this.requestStore.delete(requestId);
      }
    }
  }
}

/**
 * Enhanced logger with request context support
 */
export class ContextualLogger {
  private baseLogger: Logger;
  private context: LogContext;

  constructor(baseLogger: Logger = logger, context: LogContext = {}) {
    this.baseLogger = baseLogger;
    this.context = context;
  }

  private log(level: LogLevel, message: string, extra: Record<string, unknown> = {}) {
    const logData = {
      ...this.context,
      ...extra,
      timestamp: Date.now(),
    };

    this.baseLogger[level](logData, message);
  }

  trace(message: string, extra?: Record<string, unknown>) {
    this.log('trace', message, extra);
  }

  debug(message: string, extra?: Record<string, unknown>) {
    this.log('debug', message, extra);
  }

  info(message: string, extra?: Record<string, unknown>) {
    this.log('info', message, extra);
  }

  warn(message: string, extra?: Record<string, unknown>) {
    this.log('warn', message, extra);
  }

  error(message: string, error?: Error, extra?: Record<string, unknown>) {
    this.log('error', message, { ...extra, error });
  }

  fatal(message: string, error?: Error, extra?: Record<string, unknown>) {
    this.log('fatal', message, { ...extra, error });
  }

  child(context: LogContext): ContextualLogger {
    return new ContextualLogger(this.baseLogger, { ...this.context, ...context });
  }

  withRequestId(requestId: string): ContextualLogger {
    const requestContext = RequestTracker.getContext(requestId) || {};
    return this.child(requestContext);
  }

  // Performance logging helpers
  startTimer(operation: string): () => void {
    const start = Date.now();
    return () => {
      const duration = Date.now() - start;
      this.info(`Operation completed: ${operation}`, { operation, duration });
    };
  }

  logRequest(method: string, path: string, requestId: string) {
    this.info(`Request started: ${method} ${path}`, {
      method,
      path,
      requestId,
      type: 'request_start',
    });
  }

  logResponse(method: string, path: string, statusCode: number, duration: number, requestId: string) {
    this.info(`Request completed: ${method} ${path}`, {
      method,
      path,
      statusCode,
      duration,
      requestId,
      type: 'request_end',
    });
  }

  logToolExecution(toolName: string, requestId: string, success: boolean, duration?: number, error?: Error) {
    const level = success ? 'info' : 'error';
    const message = `Tool execution ${success ? 'completed' : 'failed'}: ${toolName}`;

    if (level === 'error') {
      this.error(message, error, {
        toolName,
        requestId,
        success,
        duration,
        type: 'tool_execution',
      });
    } else {
      this.info(message, {
        toolName,
        requestId,
        success,
        duration,
        type: 'tool_execution',
      });
    }
  }
}

/**
 * Default contextual logger instance
 */
export const contextLogger = new ContextualLogger(logger);

/**
 * Cleanup interval for request tracking
 */
setInterval(() => {
  RequestTracker.cleanup();
}, 60 * 60 * 1000); // Run every hour
