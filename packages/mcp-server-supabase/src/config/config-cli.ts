#!/usr/bin/env node
import { contextLogger } from '../utils/logger.js';
import { 
  ConfigFactory, 
  ConfigManager, 
  ConfigUtils,
  configHealthMonitor,
  type ConfigProfile,
  type ConfigHealthStatus 
} from './index.js';

/**
 * Configuration CLI for managing and monitoring configuration
 */
export class ConfigCLI {
  private logger = contextLogger.child({ component: 'ConfigCLI' });

  /**
   * Create a new configuration instance
   */
  async createConfig(
    name: string,
    profile: ConfigProfile,
    options: {
      configFiles?: string[];
      envPrefix?: string;
      enableHotReload?: boolean;
    } = {}
  ): Promise<ConfigManager> {
    this.logger.info('Creating configuration instance', { name, profile, options });
    
    try {
      const config = await ConfigFactory.create(name, {
        profile,
        autoLoad: true,
        ...options,
      });
      
      this.logger.info('Configuration instance created successfully', { 
        name, 
        profile,
        keys: config.keys().length,
      });
      
      return config;
    } catch (error) {
      this.logger.error('Failed to create configuration instance', error instanceof Error ? error : undefined);
      throw error;
    }
  }

  /**
   * List all configuration instances
   */
  listConfigs(): void {
    const instances = ConfigFactory.list();
    const summary = ConfigFactory.getSummary();
    
    console.log('\n=== Configuration Instances ===');
    
    if (instances.length === 0) {
      console.log('No configuration instances found.');
      return;
    }

    for (const instanceName of instances) {
      const instanceSummary = summary[instanceName];
      if (instanceSummary) {
        console.log(`\n${instanceName}:`);
        console.log(`  Profile: ${instanceSummary.profile}`);
        console.log(`  Keys: ${instanceSummary.keys}`);
        console.log(`  Last Updated: ${instanceSummary.lastUpdated.toISOString()}`);
        console.log(`  Sources:`);
        
        for (const [source, count] of Object.entries(instanceSummary.sources)) {
          if (count > 0) {
            console.log(`    ${source}: ${count}`);
          }
        }
      }
    }
  }

  /**
   * Show configuration details
   */
  showConfig(name: string, includeSensitive: boolean = false): void {
    const config = ConfigFactory.get(name);
    if (!config) {
      console.error(`Configuration instance '${name}' not found.`);
      return;
    }

    const configData = ConfigFactory.exportConfig(name, includeSensitive);
    console.log(`\n=== Configuration: ${name} ===`);
    console.log(JSON.stringify(configData, null, 2));
  }

  /**
   * Validate configuration instances
   */
  async validateConfigs(instanceName?: string): Promise<void> {
    console.log('\n=== Configuration Validation ===');
    
    if (instanceName) {
      const config = ConfigFactory.get(instanceName);
      if (!config) {
        console.error(`Configuration instance '${instanceName}' not found.`);
        return;
      }
      
      const validation = await config.validate();
      this.printValidationResult(instanceName, validation);
    } else {
      const validation = await ConfigFactory.validateAll();
      
      console.log(`Overall Status: ${validation.valid ? '✅ VALID' : '❌ INVALID'}`);
      console.log();
      
      for (const [name, result] of Object.entries(validation.results)) {
        const status = result.valid ? '✅ VALID' : '❌ INVALID';
        console.log(`${name}: ${status}`);
        
        if (result.errors > 0) {
          console.log(`  Errors: ${result.errors}`);
        }
        
        if (result.warnings > 0) {
          console.log(`  Warnings: ${result.warnings}`);
        }
      }
    }
  }

  /**
   * Monitor configuration health
   */
  async monitorHealth(duration?: number): Promise<void> {
    console.log('\n=== Configuration Health Monitoring ===');
    
    if (duration) {
      console.log(`Monitoring for ${duration}ms...`);
      configHealthMonitor.startMonitoring(Math.min(duration / 10, 5000));
      
      await new Promise(resolve => setTimeout(resolve, duration));
      
      configHealthMonitor.stopMonitoring();
    }
    
    const healthStatus = await configHealthMonitor.checkHealth();
    this.printHealthStatus(healthStatus);
  }

  /**
   * Compare two configuration instances
   */
  compareConfigs(name1: string, name2: string): void {
    const config1 = ConfigFactory.get(name1);
    const config2 = ConfigFactory.get(name2);
    
    if (!config1) {
      console.error(`Configuration instance '${name1}' not found.`);
      return;
    }
    
    if (!config2) {
      console.error(`Configuration instance '${name2}' not found.`);
      return;
    }
    
    const comparison = ConfigUtils.compare(config1, config2);
    
    console.log(`\n=== Configuration Comparison: ${name1} vs ${name2} ===`);
    
    if (comparison.added.length > 0) {
      console.log(`\nAdded in ${name2}:`);
      comparison.added.forEach(key => console.log(`  + ${key}`));
    }
    
    if (comparison.removed.length > 0) {
      console.log(`\nRemoved from ${name2}:`);
      comparison.removed.forEach(key => console.log(`  - ${key}`));
    }
    
    if (comparison.changed.length > 0) {
      console.log(`\nChanged:`);
      comparison.changed.forEach(change => {
        console.log(`  ~ ${change.key}: ${change.oldValue} → ${change.newValue}`);
      });
    }
    
    if (comparison.unchanged.length > 0) {
      console.log(`\nUnchanged: ${comparison.unchanged.length} keys`);
    }
  }

  /**
   * Export configuration backup
   */
  backup(name: string, outputPath?: string): void {
    const config = ConfigFactory.get(name);
    if (!config) {
      console.error(`Configuration instance '${name}' not found.`);
      return;
    }
    
    const backup = ConfigUtils.createBackup(config);
    const backupData = JSON.stringify(backup, null, 2);
    
    if (outputPath) {
      require('fs').writeFileSync(outputPath, backupData);
      console.log(`Configuration backup saved to: ${outputPath}`);
    } else {
      console.log('\n=== Configuration Backup ===');
      console.log(backupData);
    }
  }

  /**
   * Show configuration metrics
   */
  showMetrics(): void {
    const metrics = configHealthMonitor.getMetrics();
    
    console.log('\n=== Configuration Metrics ===');
    
    for (const [key, value] of Object.entries(metrics)) {
      console.log(`${key}: ${value}`);
    }
  }

  /**
   * Print validation result
   */
  private printValidationResult(name: string, validation: any): void {
    const status = validation.valid ? '✅ VALID' : '❌ INVALID';
    console.log(`${name}: ${status}`);
    
    if (validation.errors.length > 0) {
      console.log('\nErrors:');
      validation.errors.forEach((error: any) => {
        console.log(`  ❌ ${error.key}: ${error.message}`);
      });
    }
    
    if (validation.warnings.length > 0) {
      console.log('\nWarnings:');
      validation.warnings.forEach((warning: any) => {
        console.log(`  ⚠️  ${warning.key}: ${warning.message}`);
      });
    }
  }

  /**
   * Print health status
   */
  private printHealthStatus(health: ConfigHealthStatus): void {
    const status = health.healthy ? '✅ HEALTHY' : '❌ UNHEALTHY';
    console.log(`\nHealth Status: ${status}`);
    console.log(`Instances: ${health.instanceCount}`);
    console.log(`Validation Time: ${health.performanceMetrics.validationTime}ms`);
    console.log(`Memory Usage: ${Math.round(health.performanceMetrics.memoryUsage / 1024 / 1024)}MB`);
    
    if (health.issues.length > 0) {
      console.log('\nIssues:');
      health.issues.forEach(issue => {
        const icon = issue.severity === 'error' ? '❌' : '⚠️';
        console.log(`  ${icon} [${issue.instance}] ${issue.message}`);
        if (issue.key) {
          console.log(`      Key: ${issue.key}`);
        }
      });
    }
  }

  /**
   * Show help information
   */
  showHelp(): void {
    console.log(`
Configuration Management CLI

Usage:
  config-cli <command> [options]

Commands:
  create <name> <profile>      Create new configuration instance
  list                         List all configuration instances  
  show <name> [--sensitive]    Show configuration details
  validate [name]              Validate configuration(s)
  compare <name1> <name2>      Compare two configurations
  monitor [duration]           Monitor configuration health
  backup <name> [path]         Export configuration backup
  metrics                      Show configuration metrics
  help                         Show this help message

Profiles:
  development                  Development environment
  testing                      Testing environment  
  production                   Production environment
  local                        Local development

Examples:
  config-cli create myapp development
  config-cli list
  config-cli show myapp --sensitive
  config-cli validate
  config-cli compare dev prod
  config-cli monitor 30000
  config-cli backup myapp ./backup.json
  config-cli metrics
`);
  }
}

/**
 * Run the CLI
 */
async function runCLI(): Promise<void> {
  const cli = new ConfigCLI();
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    cli.showHelp();
    return;
  }
  
  const command = args[0];
  
  try {
    switch (command) {
      case 'create':
        if (args.length < 3) {
          console.error('Usage: config-cli create <name> <profile>');
          return;
        }
        await cli.createConfig(args[1], args[2] as ConfigProfile);
        break;
        
      case 'list':
        cli.listConfigs();
        break;
        
      case 'show':
        if (args.length < 2) {
          console.error('Usage: config-cli show <name> [--sensitive]');
          return;
        }
        cli.showConfig(args[1], args.includes('--sensitive'));
        break;
        
      case 'validate':
        await cli.validateConfigs(args[1]);
        break;
        
      case 'compare':
        if (args.length < 3) {
          console.error('Usage: config-cli compare <name1> <name2>');
          return;
        }
        cli.compareConfigs(args[1], args[2]);
        break;
        
      case 'monitor':
        const duration = args[1] ? parseInt(args[1]) : undefined;
        await cli.monitorHealth(duration);
        break;
        
      case 'backup':
        if (args.length < 2) {
          console.error('Usage: config-cli backup <name> [path]');
          return;
        }
        cli.backup(args[1], args[2]);
        break;
        
      case 'metrics':
        cli.showMetrics();
        break;
        
      case 'help':
      default:
        cli.showHelp();
        break;
    }
  } catch (error) {
    console.error('Command failed:', error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

// Export CLI class for programmatic use
export { ConfigCLI };

// Run CLI if called directly
if (require.main === module) {
  runCLI().catch(error => {
    console.error('CLI execution failed:', error);
    process.exit(1);
  });
}