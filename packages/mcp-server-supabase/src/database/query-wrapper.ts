import type { SupabaseClient } from '@supabase/supabase-js';
import { contextLogger } from '../utils/logger.js';
import { DatabaseError, ValidationError, TimeoutError } from '../utils/errors.js';
import type { 
  SupabaseConnectionPool, 
  SupabaseConnection 
} from '../config/supabase-connection-pool.js';

/**
 * Query execution options
 */
export interface QueryOptions {
  timeout?: number;
  connectionType?: 'anon' | 'service';
  requireAuth?: boolean;
  metadata?: Record<string, any>;
}

/**
 * Query result interface
 */
export interface QueryResult<T = any> {
  data: T[];
  error: any;
  count?: number;
  status: number;
  statusText: string;
  executionTime: number;
  connectionId: string;
  connectionType: 'anon' | 'service';
}

/**
 * Transaction context interface
 */
export interface TransactionContext {
  connection: SupabaseConnection;
  isActive: boolean;
  operations: string[];
  startTime: Date;
}

/**
 * Database operation types
 */
export type DatabaseOperationType = 
  | 'select' 
  | 'insert' 
  | 'update' 
  | 'delete' 
  | 'rpc' 
  | 'execute_sql' 
  | 'auth' 
  | 'storage' 
  | 'realtime';

/**
 * Supabase response type
 */
export interface SupabaseResponse<T = any> {
  data: T;
  error: any;
  status: number;
  statusText: string;
  count?: number;
}

/**
 * Database query wrapper class
 */
export class DatabaseQueryWrapper {
  private readonly logger = contextLogger.child({ component: 'DatabaseQueryWrapper' });
  private readonly pool: SupabaseConnectionPool;
  private activeTransactions = new Map<string, TransactionContext>();

  constructor(pool: SupabaseConnectionPool) {
    this.pool = pool;
  }

  /**
   * Execute a raw SQL query
   */
  async executeSql<T = any>(
    query: string, 
    options: QueryOptions & { readOnly?: boolean } = {}
  ): Promise<QueryResult<T>> {
    const startTime = Date.now();
    const connectionType = this.determineConnectionType('execute_sql', options);
    
    this.logger.debug('Executing SQL query', { 
      query: this.sanitizeQueryForLogging(query),
      connectionType,
      readOnly: options.readOnly,
    });

    const connection = await this.acquireConnection(connectionType, options);
    
    try {
      // Use the exec_sql RPC function for raw SQL execution
      const response = await this.executeWithTimeout(
        () => connection.client.rpc('exec_sql', { query }),
        options.timeout
      );
      const { data, error, status, statusText } = response as SupabaseResponse<T>;

      const result: QueryResult<T> = {
        data: this.processRpcResult(data) as T[],
        error,
        status,
        statusText,
        executionTime: Date.now() - startTime,
        connectionId: connection.id,
        connectionType: connection.type,
      };

      if (error) {
        throw new DatabaseError(`SQL execution failed: ${error.message}`, error.code, {
          query: this.sanitizeQueryForLogging(query),
          connectionType,
        });
      }

      this.logSlowQuery(query, result.executionTime);
      return result;
    } finally {
      await this.releaseConnection(connection);
    }
  }

  /**
   * Execute a table query (SELECT)
   */
  async query<T = any>(
    table: string,
    options: QueryOptions & {
      select?: string;
      filters?: Record<string, any>;
      orderBy?: string;
      limit?: number;
      offset?: number;
      single?: boolean;
    } = {}
  ): Promise<QueryResult<T>> {
    const startTime = Date.now();
    const connectionType = this.determineConnectionType('select', options);
    
    this.logger.debug('Executing table query', { 
      table,
      connectionType,
      filters: options.filters,
      limit: options.limit,
    });

    const connection = await this.acquireConnection(connectionType, options);
    
    try {
      let query = connection.client.from(table);
      
      // Apply select
      if (options.select) {
        query = query.select(options.select);
      } else {
        query = query.select('*');
      }
      
      // Apply filters
      if (options.filters) {
        for (const [key, value] of Object.entries(options.filters)) {
          if (Array.isArray(value)) {
            query = query.in(key, value);
          } else if (typeof value === 'object' && value !== null) {
            // Handle range queries, etc.
            for (const [op, val] of Object.entries(value)) {
              query = this.applyFilter(query, key, op, val);
            }
          } else {
            query = query.eq(key, value);
          }
        }
      }
      
      // Apply ordering
      if (options.orderBy) {
        const [column, direction] = options.orderBy.split(' ');
        query = query.order(column.trim(), { 
          ascending: direction?.toLowerCase() !== 'desc' 
        });
      }
      
      // Apply pagination
      if (options.limit) {
        query = query.limit(options.limit);
      }
      if (options.offset) {
        query = query.range(options.offset, (options.offset + (options.limit || 100)) - 1);
      }

      const response = await this.executeWithTimeout(
        () => options.single ? query.single() : query,
        options.timeout
      );
      const { data, error, status, statusText, count } = response as SupabaseResponse<T>;

      const result: QueryResult<T> = {
        data: data ? (Array.isArray(data) ? data : [data]) : [],
        error,
        count,
        status,
        statusText,
        executionTime: Date.now() - startTime,
        connectionId: connection.id,
        connectionType: connection.type,
      };

      if (error) {
        throw new DatabaseError(`Table query failed: ${error.message}`, error.code, {
          table,
          connectionType,
        });
      }

      this.logSlowQuery(`SELECT from ${table}`, result.executionTime);
      return result;
    } finally {
      await this.releaseConnection(connection);
    }
  }

  /**
   * Insert data into a table
   */
  async insert<T = any>(
    table: string,
    data: Partial<T> | Partial<T>[],
    options: QueryOptions & {
      onConflict?: string;
      returning?: string;
      upsert?: boolean;
    } = {}
  ): Promise<QueryResult<T>> {
    const startTime = Date.now();
    const connectionType = this.determineConnectionType('insert', options);
    
    this.logger.debug('Executing insert operation', { 
      table,
      connectionType,
      recordCount: Array.isArray(data) ? data.length : 1,
    });

    const connection = await this.acquireConnection(connectionType, options);
    
    try {
      let query = connection.client.from(table);
      
      if (options.upsert) {
        query = query.upsert(data as any);
      } else {
        query = query.insert(data as any);
      }
      
      if (options.onConflict) {
        // Handle conflict resolution if supported
      }
      
      if (options.returning) {
        query = query.select(options.returning);
      }

      const { data: resultData, error, status, statusText, count } = await this.executeWithTimeout<SupabaseResponse<T>>(
        () => query,
        options.timeout
      );

      const result: QueryResult<T> = {
        data: resultData || [],
        error,
        count,
        status,
        statusText,
        executionTime: Date.now() - startTime,
        connectionId: connection.id,
        connectionType: connection.type,
      };

      if (error) {
        throw new DatabaseError(`Insert operation failed: ${error.message}`, error.code, {
          table,
          connectionType,
        });
      }

      this.logSlowQuery(`INSERT into ${table}`, result.executionTime);
      return result;
    } finally {
      await this.releaseConnection(connection);
    }
  }

  /**
   * Update data in a table
   */
  async update<T = any>(
    table: string,
    data: Partial<T>,
    filters: Record<string, any>,
    options: QueryOptions & {
      returning?: string;
    } = {}
  ): Promise<QueryResult<T>> {
    const startTime = Date.now();
    const connectionType = this.determineConnectionType('update', options);
    
    this.logger.debug('Executing update operation', { 
      table,
      connectionType,
      filters,
    });

    const connection = await this.acquireConnection(connectionType, options);
    
    try {
      let query = connection.client.from(table).update(data as any);
      
      // Apply filters
      for (const [key, value] of Object.entries(filters)) {
        if (Array.isArray(value)) {
          query = query.in(key, value);
        } else {
          query = query.eq(key, value);
        }
      }
      
      if (options.returning) {
        query = query.select(options.returning);
      }

      const { data: resultData, error, status, statusText, count } = await this.executeWithTimeout<SupabaseResponse<T>>(
        () => query,
        options.timeout
      );

      const result: QueryResult<T> = {
        data: resultData || [],
        error,
        count,
        status,
        statusText,
        executionTime: Date.now() - startTime,
        connectionId: connection.id,
        connectionType: connection.type,
      };

      if (error) {
        throw new DatabaseError(`Update operation failed: ${error.message}`, error.code, {
          table,
          connectionType,
        });
      }

      this.logSlowQuery(`UPDATE ${table}`, result.executionTime);
      return result;
    } finally {
      await this.releaseConnection(connection);
    }
  }

  /**
   * Delete data from a table
   */
  async delete<T = any>(
    table: string,
    filters: Record<string, any>,
    options: QueryOptions & {
      returning?: string;
    } = {}
  ): Promise<QueryResult<T>> {
    const startTime = Date.now();
    const connectionType = this.determineConnectionType('delete', options);
    
    this.logger.debug('Executing delete operation', { 
      table,
      connectionType,
      filters,
    });

    const connection = await this.acquireConnection(connectionType, options);
    
    try {
      let query = connection.client.from(table).delete();
      
      // Apply filters
      for (const [key, value] of Object.entries(filters)) {
        if (Array.isArray(value)) {
          query = query.in(key, value);
        } else {
          query = query.eq(key, value);
        }
      }
      
      if (options.returning) {
        query = query.select(options.returning);
      }

      const { data: resultData, error, status, statusText, count } = await this.executeWithTimeout<SupabaseResponse<T>>(
        () => query,
        options.timeout
      );

      const result: QueryResult<T> = {
        data: resultData || [],
        error,
        count,
        status,
        statusText,
        executionTime: Date.now() - startTime,
        connectionId: connection.id,
        connectionType: connection.type,
      };

      if (error) {
        throw new DatabaseError(`Delete operation failed: ${error.message}`, error.code, {
          table,
          connectionType,
        });
      }

      this.logSlowQuery(`DELETE from ${table}`, result.executionTime);
      return result;
    } finally {
      await this.releaseConnection(connection);
    }
  }

  /**
   * Execute a stored procedure/RPC function
   */
  async rpc<T = any>(
    functionName: string,
    parameters: Record<string, any> = {},
    options: QueryOptions = {}
  ): Promise<QueryResult<T>> {
    const startTime = Date.now();
    const connectionType = this.determineConnectionType('rpc', options);
    
    this.logger.debug('Executing RPC function', { 
      functionName,
      connectionType,
      parameters: Object.keys(parameters),
    });

    const connection = await this.acquireConnection(connectionType, options);
    
    try {
      const { data, error, status, statusText } = await this.executeWithTimeout<SupabaseResponse<T>>(
        () => connection.client.rpc(functionName, parameters),
        options.timeout
      );

      const result: QueryResult<T> = {
        data: data ? (Array.isArray(data) ? data : [data]) : [],
        error,
        status,
        statusText,
        executionTime: Date.now() - startTime,
        connectionId: connection.id,
        connectionType: connection.type,
      };

      if (error) {
        throw new DatabaseError(`RPC function failed: ${error.message}`, error.code, {
          functionName,
          connectionType,
        });
      }

      this.logSlowQuery(`RPC ${functionName}`, result.executionTime);
      return result;
    } finally {
      await this.releaseConnection(connection);
    }
  }

  /**
   * Begin a transaction (Note: Supabase doesn't support traditional transactions via client)
   */
  async beginTransaction(options: QueryOptions = {}): Promise<string> {
    const connectionType = this.determineConnectionType('execute_sql', options);
    const connection = await this.acquireConnection(connectionType, options);
    
    const transactionId = this.generateTransactionId();
    const context: TransactionContext = {
      connection,
      isActive: true,
      operations: [],
      startTime: new Date(),
    };
    
    this.activeTransactions.set(transactionId, context);
    
    this.logger.debug('Transaction begun', { 
      transactionId,
      connectionId: connection.id,
      connectionType: connection.type,
    });
    
    return transactionId;
  }

  /**
   * Execute operation within a transaction
   */
  async executeInTransaction<T = any>(
    transactionId: string,
    operation: (client: SupabaseClient) => Promise<any>
  ): Promise<T> {
    const context = this.activeTransactions.get(transactionId);
    if (!context || !context.isActive) {
      throw new DatabaseError('Invalid or inactive transaction', 'INVALID_TRANSACTION');
    }

    try {
      const result = await operation(context.connection.client);
      context.operations.push(`Operation executed at ${new Date().toISOString()}`);
      return result;
    } catch (error) {
      context.operations.push(`Operation failed at ${new Date().toISOString()}: ${error}`);
      throw error;
    }
  }

  /**
   * Commit a transaction
   */
  async commitTransaction(transactionId: string): Promise<void> {
    const context = this.activeTransactions.get(transactionId);
    if (!context || !context.isActive) {
      throw new DatabaseError('Invalid or inactive transaction', 'INVALID_TRANSACTION');
    }

    try {
      context.isActive = false;
      this.activeTransactions.delete(transactionId);
      
      this.logger.debug('Transaction committed', { 
        transactionId,
        operationCount: context.operations.length,
        duration: Date.now() - context.startTime.getTime(),
      });
    } finally {
      await this.releaseConnection(context.connection);
    }
  }

  /**
   * Rollback a transaction
   */
  async rollbackTransaction(transactionId: string): Promise<void> {
    const context = this.activeTransactions.get(transactionId);
    if (!context || !context.isActive) {
      throw new DatabaseError('Invalid or inactive transaction', 'INVALID_TRANSACTION');
    }

    try {
      context.isActive = false;
      this.activeTransactions.delete(transactionId);
      
      this.logger.debug('Transaction rolled back', { 
        transactionId,
        operationCount: context.operations.length,
        duration: Date.now() - context.startTime.getTime(),
      });
    } finally {
      await this.releaseConnection(context.connection);
    }
  }

  /**
   * Determine the appropriate connection type for an operation
   */
  private determineConnectionType(
    operationType: DatabaseOperationType, 
    options: QueryOptions
  ): 'anon' | 'service' {
    // Explicit connection type specified
    if (options.connectionType) {
      return options.connectionType;
    }

    // Operations that typically require service role
    if (['execute_sql', 'insert', 'update', 'delete'].includes(operationType)) {
      return 'service';
    }

    // Auth operations that require authentication
    if (options.requireAuth) {
      return 'service';
    }

    // Default to anonymous for read operations
    return 'anon';
  }

  /**
   * Acquire a connection from the pool
   */
  private async acquireConnection(
    type: 'anon' | 'service',
    options: QueryOptions
  ): Promise<SupabaseConnection> {
    try {
      return await this.pool.acquire(type);
    } catch (error) {
      throw new DatabaseError(
        `Failed to acquire ${type} connection: ${error}`, 
        'CONNECTION_ACQUISITION_FAILED',
        { connectionType: type }
      );
    }
  }

  /**
   * Release a connection back to the pool
   */
  private async releaseConnection(connection: SupabaseConnection): Promise<void> {
    try {
      await this.pool.release(connection);
    } catch (error) {
      this.logger.error('Failed to release connection', error instanceof Error ? error : undefined, {
        connectionId: connection.id,
        connectionType: connection.type,
      });
    }
  }

  /**
   * Execute an operation with timeout
   */
  private async executeWithTimeout<T>(
    operation: () => Promise<T>,
    timeout?: number
  ): Promise<T> {
    if (!timeout) {
      return await operation();
    }

    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new TimeoutError(`Operation timed out after ${timeout}ms`));
      }, timeout);
    });

    return await Promise.race([operation(), timeoutPromise]);
  }

  /**
   * Apply filter to query
   */
  private applyFilter(query: any, key: string, operator: string, value: any): any {
    switch (operator) {
      case 'gt': return query.gt(key, value);
      case 'gte': return query.gte(key, value);
      case 'lt': return query.lt(key, value);
      case 'lte': return query.lte(key, value);
      case 'neq': return query.neq(key, value);
      case 'like': return query.like(key, value);
      case 'ilike': return query.ilike(key, value);
      case 'is': return query.is(key, value);
      case 'in': return query.in(key, value);
      case 'contains': return query.contains(key, value);
      case 'containedBy': return query.containedBy(key, value);
      case 'rangeGt': return query.rangeGt(key, value);
      case 'rangeGte': return query.rangeGte(key, value);
      case 'rangeLt': return query.rangeLt(key, value);
      case 'rangeLte': return query.rangeLte(key, value);
      case 'rangeAdjacent': return query.rangeAdjacent(key, value);
      case 'overlaps': return query.overlaps(key, value);
      case 'textSearch': return query.textSearch(key, value);
      default: return query.eq(key, value);
    }
  }

  /**
   * Process RPC result data
   */
  private processRpcResult(data: any): any[] {
    if (!data) return [];
    
    // Handle different RPC result formats
    if (Array.isArray(data)) {
      return data;
    }
    
    if (typeof data === 'object' && data !== null) {
      // Check for nested result
      if ('result' in data && Array.isArray(data.result)) {
        return data.result;
      }
      
      // Check for exec_sql format
      if ('exec_sql' in data && Array.isArray(data.exec_sql)) {
        return data.exec_sql;
      }
      
      // Single object result
      return [data];
    }
    
    return [];
  }

  /**
   * Sanitize query for logging (remove sensitive data)
   */
  private sanitizeQueryForLogging(query: string): string {
    // Remove potential passwords or sensitive data from logs
    return query
      .replace(/password\s*=\s*'[^']*'/gi, "password='***'")
      .replace(/token\s*=\s*'[^']*'/gi, "token='***'")
      .substring(0, 500); // Limit length
  }

  /**
   * Log slow queries
   */
  private logSlowQuery(operation: string, executionTime: number): void {
    const threshold = 2000; // 2 seconds default threshold
    if (executionTime > threshold) {
      this.logger.warn('Slow query detected', {
        operation,
        executionTime,
        threshold,
      });
    }
  }

  /**
   * Generate unique transaction ID
   */
  private generateTransactionId(): string {
    return `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * Create a database query wrapper instance
 */
export function createDatabaseQueryWrapper(pool: SupabaseConnectionPool): DatabaseQueryWrapper {
  return new DatabaseQueryWrapper(pool);
}